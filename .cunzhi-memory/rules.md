# 开发规范和规则

- MishiAPI 项目 GoFrame 开发规范：

1. 目录结构：api/接口定义，internal/cmd/启动，internal/controller/控制器（自动生成禁改），internal/dao/数据访问（自动生成禁改），internal/logic/业务逻辑（手动编写），internal/service/服务接口（自动生成禁改），internal/model/数据模型（自动生成禁改）

2. 开发流程：Logic层手动编写 → gf gen service生成接口 → gf gen ctrl生成控制器 → Controller仅调用Service

3. 代码生成规则：严禁修改自动生成代码，文件头包含DO NOT EDIT标识，重新生成会覆盖

4. 命令行工具顺序：gf gen dao → gf gen service → gf gen ctrl → gf gen pb

5. 分层职责：API层定义契约，Controller层处理HTTP，Service层定义接口，Logic层实现业务，DAO层访问数据

6. 错误处理：统一错误码在internal/logic/errcode/，响应格式{message,data}，使用gcode.Code接口

7. 命名规范：包名小写，结构体大驼峰，方法公开大驼峰私有小驼峰，常量大写下划线，文件小写下划线

8. 配置管理：manifest/config/config.yaml，g.Cfg()读取，敏感信息环境变量

9. Git提交：feat/fix/docs/style/refactor/chore前缀

10. 注释规范：公开函数必须注释，结构体字段用dc标签，API用summary和tags
