package v1

import "github.com/gogf/gf/v2/frame/g"
type TgrjReq struct {
	g.Meta `path:"/rand/dog" method:"get" summary:"随机获取舔狗日记" tags:"随机"`
}
type TgrjRes struct {
	Content string `json:"content"`
}

type BgmReq struct {
	g.<PERSON>a `path:"/rand/bgm" method:"get" summary:"随机获取音乐" tags:"随机"`
}
type BgmRes struct {
  SongUrl string `json:"song_url" dc:"歌曲播放链接"`
}
type AnimeReq struct{
	g.Meta `path:"/rand/anime/pic" method:"get" summary:"随机获取动漫图片" tags:"随机"`
}
type AnimeRes struct{
	Imgurl string `json:"imgurl" dc:"图片链接"`
}
