package v1

import "github.com/gogf/gf/v2/frame/g"
type QQInfoReq struct {
	g.<PERSON>a `path:"/qq/info/:qq" method:"GET" summary:"获取QQ信息" tags:"QQ"`
	QQ    string `path:"qq" v:"required|qq#请输入QQ号|请输入有效的QQ号" dc:"QQ号"`
}
type QQInfoRes struct {
	Qq        int64  `json:"qq"`
		Nickname  string `json:"nickname" dc:"昵称"`
		Sex       string `json:"sex" dc:"性别"`
		Age       int    `json:"age" dc:"年龄"`
		Level     int    `json:"level" dc:"等级"`
		Qid       string `json:"qid" dc:"QID"`
		VipLevel  string `json:"vip_level" dc:"VIP等级"`
		LoginDays int    `json:"login_days" dc:"登录天数"`
		Sing      string `json:"sing" dc:"签名"`
		Avatar    string `json:"avatar" dc:"头像"`
		RegTime   int    `json:"regTime" dc:"注册时间"`
}
