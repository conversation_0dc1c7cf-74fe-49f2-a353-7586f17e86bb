package v1

import "github.com/gogf/gf/v2/frame/g"

type DouyinVideoReq struct {
	g.<PERSON>a `path:"/douyin/video"   method:"get" summary:"抖音视频解析" tags:"解析"`
	Url    string `v:"required|url#请输入视频链接 | 请输入有效的URL"  dc:"视频链接"`
}
type DouyinVideoRes struct {
	Title    string `json:"title" dc:"视频标题"`
	Author   string `json:"author" dc:"视频作者"`
	Like     int    `json:"like" dc:"点赞数"`
	Time     int    `json:"time" dc:"发布时间"`
	VideoUrl string `json:"video_url" dc:"视频链接"`
}
type VipVideoReq struct {
	g.Meta `path:"/vip/video"   method:"get" summary:"VIP视频解析" tags:"解析"`
	Url    string `v:"required|url#请输入视频链接 | 请输入有效的URL"  dc:"视频链接"`
}
type VipVideoRes map[string]any
