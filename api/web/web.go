// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package web

import (
	"context"

	"MishiAPI/api/web/v1"
)

type IWebV1 interface {
	Download(ctx context.Context, req *v1.DownloadReq) (res *v1.DownloadRes, err error)
	Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error)
	Bgm(ctx context.Context, req *v1.BgmReq) (res *v1.BgmRes, err error)
	Anime(ctx context.Context, req *v1.AnimeReq) (res *v1.AnimeRes, err error)
	QQInfo(ctx context.Context, req *v1.QQInfoReq) (res *v1.QQInfoRes, err error)
	DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error)
	VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error)
}
