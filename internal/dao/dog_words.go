// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"MishiAPI/internal/dao/internal"
)

// dogWordsDao is the data access object for the table dog_words.
// You can define custom methods on it to extend its functionality as needed.
type dogWordsDao struct {
	*internal.DogWordsDao
}

var (
	// DogWords is a globally accessible object for table dog_words operations.
	DogWords = dogWordsDao{internal.NewDogWordsDao()}
)

// Add your custom methods and functionality below.
