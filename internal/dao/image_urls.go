// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"MishiAPI/internal/dao/internal"
)

// imageUrlsDao is the data access object for the table image_urls.
// You can define custom methods on it to extend its functionality as needed.
type imageUrlsDao struct {
	*internal.ImageUrlsDao
}

var (
	// ImageUrls is a globally accessible object for table image_urls operations.
	ImageUrls = imageUrlsDao{internal.NewImageUrlsDao()}
)

// Add your custom methods and functionality below.
