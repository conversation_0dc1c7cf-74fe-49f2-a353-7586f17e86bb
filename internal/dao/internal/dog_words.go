// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DogWordsDao is the data access object for the table dog_words.
type DogWordsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  DogWordsColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// DogWordsColumns defines and stores column names for the table dog_words.
type DogWordsColumns struct {
	Id         string // 主键
	Content    string // 内容
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

// dogWordsColumns holds the columns for the table dog_words.
var dogWordsColumns = DogWordsColumns{
	Id:         "id",
	Content:    "content",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewDogWordsDao creates and returns a new DAO object for table data access.
func NewDogWordsDao(handlers ...gdb.ModelHandler) *DogWordsDao {
	return &DogWordsDao{
		group:    "default",
		table:    "dog_words",
		columns:  dogWordsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DogWordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DogWordsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DogWordsDao) Columns() DogWordsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DogWordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DogWordsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DogWordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
