package web

import (
	v1 "MishiAPI/api/web/v1"
	"MishiAPI/internal/service"
	"context"
	"errors"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sDownload struct{}

func init() {
	service.RegisterDownload(Download())
}
func Download() *sDownload {
	return &sDownload{}
}

func (s *sDownload) Download(ctx context.Context, req *v1.DownloadReq) (res *v1.DownloadRes, err error) {
	// 创建GoFrame HTTP客户端，设置超时时间
	client := g.Client().Timeout(30 * time.Second)
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送HTTP请求
	resp, err := client.Get(ctx, req.Url)
	if err != nil {
		g.Log().Errorf(ctx, "HTTP请求失败: %s, 错误: %v", req.Url, err)
		return nil, gerror.New("请求资源失败")
	}
	defer resp.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		g.Log().Errorf(ctx, "HTTP请求返回错误状态码: %d, URL: %s", resp.StatusCode, req.Url)
		return nil, gerror.Newf("资源不可访问，状态码: %d", resp.StatusCode)
	}

	// 获取当前HTTP响应对象
	r := g.RequestFromCtx(ctx)
	if r == nil {
		g.Log().Errorf(ctx, "无法获取HTTP响应对象")
		return nil, gerror.New("系统错误")
	}

	// 透传响应头
	if contentType := resp.Header.Get("Content-Type"); contentType != "" {
		r.Response.Header().Set("Content-Type", contentType)
	}
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		r.Response.Header().Set("Content-Length", contentLength)
	}

	// 流式传输文件内容
	_, err = io.Copy(r.Response.Writer, resp.Body)
	if err != nil {
		// 根据GoFrame官方文档，客户端断开连接是正常现象
		// 检查是否为客户端断开连接的各种情况
		if !isClientDisconnectError(err) {
			g.Log().Errorf(ctx, "文件流传输失败: %v", err)
			return nil, gerror.New("文件传输失败")
		}
	}

	return &v1.DownloadRes{}, nil
}

// isClientDisconnectError 检查错误是否为客户端断开连接相关的错误
func isClientDisconnectError(err error) bool {
	if err == nil {
		return false
	}

	// 检查context取消
	if errors.Is(err, context.Canceled) {
		return true
	}

	// 检查EOF错误
	if err.Error() == "EOF" {
		return true
	}

	// 检查网络连接错误
	var netErr *net.OpError
	if errors.As(err, &netErr) {
		return true
	}

	// 检查常见的客户端断开连接错误消息
	errMsg := strings.ToLower(err.Error())
	clientDisconnectKeywords := []string{
		"connection reset by peer",
		"broken pipe",
		"connection aborted",
		"use of closed network connection",
	}

	for _, keyword := range clientDisconnectKeywords {
		if strings.Contains(errMsg, keyword) {
			return true
		}
	}

	return false
}
