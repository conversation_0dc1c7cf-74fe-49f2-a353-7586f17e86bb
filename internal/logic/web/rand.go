package web

import (
	v1 "MishiAPI/api/web/v1"
	"MishiAPI/internal/dao"
	"MishiAPI/internal/model/entity"
	"MishiAPI/internal/service"
	"context"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sRand struct{}

func init() {
	service.RegisterRand(Rand())
}
func Rand() *sRand {
	return &sRand{}
}
func (s *sRand) Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error) {
	// 从数据库随机获取一条舔狗日记
	var dogWord *entity.DogWords
	err = dao.DogWords.Ctx(ctx).OrderRandom().Scan(&dogWord)
	if err != nil {
		return nil, err
	}
	// 返回随机获取的内容
	res = &v1.TgrjRes{
		Content: dogWord.Content,
	}
	return
}

func (s *sRand) Bgm(ctx context.Context, req *v1.BgmReq) (res *v1.BgmRes, err error) {
	// 调用外部API获取随机音乐
	apiUrl := "https://api.dragonlongzhu.cn/api/dg_qqlist.php?List_id=6286262842"

	// 创建独立的HTTP客户端，设置超时时间
	client := g.Client().Timeout(10 * time.Second)
	response := client.GetContent(ctx, apiUrl)
	if response == "" {
		g.Log().Errorf(ctx, "获取音乐数据失败，API响应为空，请求地址: %s", apiUrl)
		return nil, gerror.New("获取音乐数据失败")
	}

	// 使用gjson直接解析所需字段，简化JSON处理逻辑
	jsonObj := gjson.New(response)
	code := jsonObj.Get("code").Int()
	if code != 200 {
		g.Log().Errorf(ctx, "API返回错误状态码: %d，响应内容: %s", code, response)
		return nil, gerror.New("API返回错误状态码")
	}

	songUrl := jsonObj.Get("song_url").String()
	if songUrl == "" {
		g.Log().Errorf(ctx, "获取歌曲链接失败，song_url字段为空，响应内容: %s", response)
		return nil, gerror.New("获取歌曲链接失败")
	}

	// 返回歌曲链接
	res = &v1.BgmRes{
		SongUrl: songUrl,
	}
	return
}

func (s *sRand) Anime(ctx context.Context, req *v1.AnimeReq) (res *v1.AnimeRes, err error) {
	var anime *entity.ImageUrls
	err = dao.ImageUrls.Ctx(ctx).OrderRandom().Scan(&anime)
	if err!= nil {
		return nil, err
	}
	res = &v1.AnimeRes{
		Imgurl: anime.Url,
	}
	return
}
