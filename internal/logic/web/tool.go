package web

import (
	v1 "MishiAPI/api/web/v1"
	"MishiAPI/internal/consts"
	"MishiAPI/internal/service"
	"context"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)
type sTool struct{}

func init() {
	service.RegisterTool(Tool())
}
func Tool() *sTool {
	return &sTool{}
}
func (s *sTool) QQInfo(ctx context.Context, req *v1.QQInfoReq) (res *v1.QQInfoRes, err error) {
    // 发送API请求
    resp, err := g.Client().Timeout(15*time.Second).Get(ctx, "https://api.makuo.cc/api/get.info.qq", g.Map{
        "qq":    req.QQ,
        "token": consts.API_TOKEN_TYPE,
    })
    if err != nil {
        g.Log().Errorf(ctx, "请求QQ信息API失败: %v", err)
        return nil, gerror.New("获取QQ信息失败，请稍后重试")
    }
    defer resp.Close()

    // 解析响应并检查状态
    jsonObj := gjson.New(resp.ReadAllString())
    if code := jsonObj.Get("code").Int(); code != 200 {
        msg := jsonObj.Get("msg").String()
        g.Log().Errorf(ctx, "QQ信息API返回错误，状态码: %d, 消息: %s", code, msg)
        return nil, gerror.Newf("获取失败: %s", msg)
    }

    // 获取并验证数据
    dataObj := jsonObj.Get("data")
    if dataObj.IsNil() || !dataObj.IsMap() {
        g.Log().Errorf(ctx, "QQ信息API返回数据格式错误")
        return nil, gerror.New("获取数据格式不正确")
    }

    // 转换数据结构
    res = &v1.QQInfoRes{}
    if err := gconv.Struct(dataObj, res); err != nil {
        g.Log().Errorf(ctx, "转换QQ信息数据失败: %v", err)
        return nil, gerror.New("处理数据失败")
    }

    return res, nil
}
    