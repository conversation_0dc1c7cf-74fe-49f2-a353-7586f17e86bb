package web

import (
	v1 "MishiAPI/api/web/v1"
	"MishiAPI/internal/consts"
	"MishiAPI/internal/service"
	"context"
	"net/url"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type sVideo struct{}

func init() {
	service.RegisterVideo(Video())
}
func Video() *sVideo {
	return &sVideo{}
}

func (s *sVideo) DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error) {
	// 发送API请求，使用与QQInfo一致的请求方式
	resp, err := g.Client().Timeout(15*time.Second).Get(ctx, "https://api.makuo.cc/api/get.video.douyin", g.Map{
		"token": consts.DOUYIN_API_TOKEN,
		"url":   req.Url,
	})
	if err != nil {
		g.Log().Errorf(ctx, "请求抖音视频API失败: %v", err)
		return nil, gerror.New("获取抖音视频数据失败，请稍后重试")
	}
	defer resp.Close()

	// 读取响应内容
	response := resp.ReadAllString()
	if response == "" {
		g.Log().Errorf(ctx, "获取抖音视频数据失败，API响应为空")
		return nil, gerror.New("获取抖音视频数据失败")
	}

	// 使用gjson直接解析所需字段，简化JSON处理逻辑
	jsonObj := gjson.New(response)
	code := jsonObj.Get("code").Int()
	if code != 200 {
		g.Log().Errorf(ctx, "API返回错误状态码: %d，响应内容: %s", code, response)
		return nil, gerror.New("API返回错误状态码")
	}

	// 获取并验证数据
	dataObj := jsonObj.Get("data")
	if dataObj.IsNil() || !dataObj.IsMap() {
		g.Log().Errorf(ctx, "API响应data字段格式错误，响应内容: %s", response)
		return nil, gerror.New("视频数据格式错误")
	}

	// 使用gconv.Struct转换数据结构，保持与tool.go一致的处理方式
	res = &v1.DouyinVideoRes{}
	if err := gconv.Struct(dataObj, res); err != nil {
		g.Log().Errorf(ctx, "转换抖音视频数据失败: %v", err)
		return nil, gerror.New("处理视频数据失败")
	}
	return
}

func (s *sVideo) VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error) {
	// 创建独立的HTTP客户端，设置超时时间
	client := g.Client().Timeout(15 * time.Second)

	// 首先尝试视频解析接口
	videoUrl := "https://api.dragonlongzhu.cn/api/sp_jx/sp.php?url=" + url.QueryEscape(req.Url)
	videoResponse := client.GetContent(ctx, videoUrl)
	if videoResponse == "" {
		g.Log().Errorf(ctx, "获取VIP视频数据失败，API响应为空，请求地址: %s", videoUrl)
		return nil, gerror.New("获取VIP视频数据失败")
	}

	// 解析视频接口响应
	videoJsonObj := gjson.New(videoResponse)
	videoCode := videoJsonObj.Get("code").Int()

	// 如果视频解析成功，直接返回视频数据
	if videoCode == 200 {
		dataObj := videoJsonObj.Get("data")
		if dataObj.IsNil() || !dataObj.IsMap() {
			g.Log().Errorf(ctx, "视频API响应data字段格式错误，响应内容: %s", videoResponse)
			return nil, gerror.New("视频数据格式错误")
		}

		// VipVideoRes是map类型，直接转换
		var vipRes v1.VipVideoRes
		if err := gconv.Struct(dataObj, &vipRes); err != nil {
			g.Log().Errorf(ctx, "转换VIP视频数据失败: %v", err)
			return nil, gerror.New("处理视频数据失败")
		}
		res = &vipRes
		return
	}

	// 视频解析失败，记录日志并尝试图集解析
	g.Log().Infof(ctx, "视频解析失败，状态码: %d，尝试图集解析，请求地址: %s", videoCode, videoUrl)

	// 尝试图集解析接口
	imageUrl := "https://api.dragonlongzhu.cn/api/sp_jx/tuji.php?url=" + url.QueryEscape(req.Url)
	imageResponse := client.GetContent(ctx, imageUrl)
	if imageResponse == "" {
		g.Log().Errorf(ctx, "获取VIP图集数据失败，API响应为空，请求地址: %s", imageUrl)
		return nil, gerror.New("获取VIP数据失败")
	}

	// 解析图集接口响应
	imageJsonObj := gjson.New(imageResponse)
	imageCode := imageJsonObj.Get("code").Int()
	if imageCode != 200 {
		g.Log().Errorf(ctx, "图集API返回错误状态码: %d，响应内容: %s", imageCode, imageResponse)
		return nil, gerror.New("API返回错误状态码")
	}

	// 获取并验证图集数据
	dataObj := imageJsonObj.Get("data")
	if dataObj.IsNil() || !dataObj.IsMap() {
		g.Log().Errorf(ctx, "图集API响应data字段格式错误，响应内容: %s", imageResponse)
		return nil, gerror.New("图集数据格式错误")
	}

	// 使用gconv.Struct转换数据结构，支持多平台格式
	var vipRes v1.VipVideoRes
	if err := gconv.Struct(dataObj, &vipRes); err != nil {
		g.Log().Errorf(ctx, "转换VIP图集数据失败: %v", err)
		return nil, gerror.New("处理图集数据失败")
	}
	res = &vipRes
	return
}
