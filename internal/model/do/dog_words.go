// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DogWords is the golang structure of table dog_words for DAO operations like Where/Data.
type DogWords struct {
	g.Meta     `orm:"table:dog_words, do:true"`
	Id         interface{} // 主键
	Content    interface{} // 内容
	CreateTime *gtime.Time // 创建时间
	UpdateTime *gtime.Time // 更新时间
}
