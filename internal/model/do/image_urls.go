// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ImageUrls is the golang structure of table image_urls for DAO operations like Where/Data.
type ImageUrls struct {
	g.Meta    `orm:"table:image_urls, do:true"`
	Id        interface{} // 自增主键
	Url       interface{} // 图片完整URL
	CreatedAt *gtime.Time // 记录创建时间
}
