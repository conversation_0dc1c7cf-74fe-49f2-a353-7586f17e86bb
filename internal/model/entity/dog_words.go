// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DogWords is the golang structure for table dog_words.
type DogWords struct {
	Id         int         `json:"id"         orm:"id"          description:"主键"`   // 主键
	Content    string      `json:"content"    orm:"content"     description:"内容"`   // 内容
	CreateTime *gtime.Time `json:"createTime" orm:"create_time" description:"创建时间"` // 创建时间
	UpdateTime *gtime.Time `json:"updateTime" orm:"update_time" description:"更新时间"` // 更新时间
}
