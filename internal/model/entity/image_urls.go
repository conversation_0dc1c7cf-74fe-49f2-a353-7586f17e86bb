// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ImageUrls is the golang structure for table image_urls.
type ImageUrls struct {
	Id        int         `json:"id"        orm:"id"         description:"自增主键"`    // 自增主键
	Url       string      `json:"url"       orm:"url"        description:"图片完整URL"` // 图片完整URL
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"记录创建时间"`  // 记录创建时间
}
