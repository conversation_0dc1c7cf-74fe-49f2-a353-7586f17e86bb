// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	v1 "MishiAPI/api/web/v1"
	"context"
)

type (
	IDownload interface {
		Download(ctx context.Context, req *v1.DownloadReq) (res *v1.DownloadRes, err error)
	}
	IRand interface {
		Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error)
		Bgm(ctx context.Context, req *v1.BgmReq) (res *v1.BgmRes, err error)
		Anime(ctx context.Context, req *v1.AnimeReq) (res *v1.AnimeRes, err error)
	}
	ITool interface {
		QQInfo(ctx context.Context, req *v1.QQInfoReq) (res *v1.QQInfoRes, err error)
	}
	IVideo interface {
		DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error)
		VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error)
	}
)

var (
	localDownload IDownload
	localRand     IRand
	localTool     ITool
	localVideo    IVideo
)

func Download() IDownload {
	if localDownload == nil {
		panic("implement not found for interface IDownload, forgot register?")
	}
	return localDownload
}

func RegisterDownload(i IDownload) {
	localDownload = i
}

func Rand() IRand {
	if localRand == nil {
		panic("implement not found for interface IRand, forgot register?")
	}
	return localRand
}

func RegisterRand(i IRand) {
	localRand = i
}

func Tool() ITool {
	if localTool == nil {
		panic("implement not found for interface ITool, forgot register?")
	}
	return localTool
}

func RegisterTool(i ITool) {
	localTool = i
}

func Video() IVideo {
	if localVideo == nil {
		panic("implement not found for interface IVideo, forgot register?")
	}
	return localVideo
}

func RegisterVideo(i IVideo) {
	localVideo = i
}
